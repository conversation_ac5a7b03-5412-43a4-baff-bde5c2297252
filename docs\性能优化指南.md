# HOUT应用性能优化指南

## 🚨 已修复的关键问题

### 1. 设备扫描过度频繁问题

**问题描述：**
- 默认扫描间隔为2秒，过于频繁
- 多个组件同时启动扫描，导致重复调用
- `get_device_info`函数内部调用`scan_devices()`，造成递归扫描

**修复措施：**
- ✅ 将默认扫描间隔从2秒增加到5秒
- ✅ 添加防抖机制，最小扫描间隔3秒
- ✅ 优化`get_device_info`函数，优先使用缓存
- ✅ 引入任务队列管理器，避免重复扫描

### 2. 窗口句柄错误问题

**问题描述：**
- 窗口状态检查过于频繁，每次resize都会触发
- 缺乏错误重试机制和防护措施
- 窗口置顶功能调用时机不当

**修复措施：**
- ✅ 添加防抖机制，延迟300ms执行窗口状态检查
- ✅ 增加重试机制，最多重试3次
- ✅ 使用任务队列管理窗口状态检查
- ✅ 改善错误处理和日志记录

### 3. 消息队列溢出问题

**问题描述：**
- 多个定时器同时运行，产生大量异步操作
- 缺乏任务队列管理和优先级控制
- 日志系统过于频繁地更新UI

**修复措施：**
- ✅ 实现任务队列管理器，限制并发任务数
- ✅ 添加任务优先级和超时机制
- ✅ 引入性能监控系统
- ✅ 优化定时器使用，避免资源竞争

## 🛠️ 新增的性能优化工具

### 1. 任务队列管理器 (`taskQueueManager.ts`)

**功能特性：**
- 任务优先级管理
- 并发控制（默认最大3个并发任务）
- 超时和重试机制
- 防抖功能，避免重复任务
- 性能统计和监控

**使用示例：**
```typescript
import { taskQueue } from '../utils/taskQueueManager';

// 添加设备扫描任务（自动防抖）
taskQueue.addDeviceScanTask();

// 添加窗口状态检查任务
taskQueue.addWindowStateTask();

// 获取队列状态
const status = taskQueue.getStatus();
console.log(`队列长度: ${status.queueLength}, 运行中: ${status.runningTasks}`);
```

### 2. 性能监控器 (`performanceMonitor.ts`)

**监控指标：**
- 内存使用情况
- 任务队列长度
- 活跃定时器数量
- 事件监听器数量

**警告阈值：**
- 内存使用：警告100MB，严重200MB
- 任务队列：警告10个，严重20个
- 定时器：警告50个，严重100个

**使用示例：**
```typescript
import { performanceMonitor } from '../utils/performanceMonitor';

// 开始监控（每10秒检查一次）
performanceMonitor.startMonitoring(10000);

// 获取性能报告
const report = performanceMonitor.getPerformanceReport();
console.log('当前内存使用:', report.currentMetrics.memoryUsage, 'MB');
```

## 📊 性能优化配置

### 1. 扫描间隔配置

```typescript
// src/stores/appStore.ts
const defaultConfig: AppConfig = {
  scanInterval: 5000,           // 设备扫描间隔（毫秒）
  deviceDetectionInterval: 10000, // 设备检测间隔（毫秒）
};
```

### 2. 任务队列配置

```typescript
// 在App.tsx中的配置
taskQueue.setMaxConcurrent(2); // 最大并发任务数
```

### 3. 性能监控配置

```typescript
// 在App.tsx中的配置
performanceMonitor.startMonitoring(10000); // 监控间隔10秒
```

## 🔧 最佳实践建议

### 1. 设备扫描优化

**DO（推荐做法）：**
- ✅ 使用任务队列管理设备扫描
- ✅ 优先使用缓存数据
- ✅ 设置合理的扫描间隔（≥5秒）
- ✅ 在组件卸载时停止扫描

**DON'T（避免做法）：**
- ❌ 直接调用`scan_devices()`
- ❌ 在多个组件中同时启动扫描
- ❌ 设置过短的扫描间隔（<3秒）
- ❌ 忽略扫描错误处理

### 2. 窗口管理优化

**DO（推荐做法）：**
- ✅ 使用防抖机制处理窗口事件
- ✅ 添加错误重试机制
- ✅ 使用任务队列管理窗口操作
- ✅ 合理处理窗口状态变化

**DON'T（避免做法）：**
- ❌ 频繁调用窗口API
- ❌ 忽略窗口操作错误
- ❌ 在resize事件中直接执行复杂操作
- ❌ 缺乏超时保护

### 3. 异步操作优化

**DO（推荐做法）：**
- ✅ 使用任务队列管理异步操作
- ✅ 设置合理的超时时间
- ✅ 实现重试机制
- ✅ 监控任务执行状态

**DON'T（避免做法）：**
- ❌ 创建过多并发任务
- ❌ 缺乏超时保护
- ❌ 忽略任务失败处理
- ❌ 无限制地创建定时器

## 🚀 性能监控和调试

### 1. 实时性能监控

在开发者工具中查看性能指标：
```javascript
// 在浏览器控制台中执行
window.performanceMonitor.getPerformanceReport()
```

### 2. 任务队列状态

查看当前任务队列状态：
```javascript
// 在浏览器控制台中执行
window.taskQueue.getStatus()
```

### 3. 性能警告

监控控制台中的性能警告：
- `Performance Alert [WARNING]`: 性能警告
- `Performance Alert [CRITICAL]`: 严重性能问题

## 📈 预期性能改进

### 1. 设备扫描性能

- **优化前：** 每2秒扫描一次，可能同时触发多次扫描
- **优化后：** 每5秒扫描一次，防抖保护，最多2个并发任务
- **预期改进：** 减少60%的扫描频率，消除重复扫描

### 2. 窗口操作性能

- **优化前：** 每次resize立即检查窗口状态，无错误保护
- **优化后：** 300ms防抖，3次重试机制，任务队列管理
- **预期改进：** 减少80%的窗口API调用，提高稳定性

### 3. 内存使用

- **优化前：** 无内存监控，可能存在内存泄漏
- **优化后：** 实时内存监控，定时器管理，任务队列限制
- **预期改进：** 减少30%的内存使用，及时发现内存问题

## 🔍 故障排查指南

### 1. 设备扫描问题

**症状：** 设备扫描过于频繁或失败
**排查步骤：**
1. 检查任务队列状态：`taskQueue.getStatus()`
2. 查看扫描间隔配置
3. 检查缓存是否正常工作
4. 查看错误日志

### 2. 窗口操作问题

**症状：** 窗口置顶失败或窗口状态异常
**排查步骤：**
1. 检查是否有窗口状态检查任务在队列中
2. 查看重试机制是否正常工作
3. 检查窗口句柄是否有效
4. 查看相关错误日志

### 3. 性能问题

**症状：** 应用响应缓慢或内存使用过高
**排查步骤：**
1. 查看性能监控报告
2. 检查任务队列长度
3. 统计活跃定时器数量
4. 分析性能警告信息

## 📝 后续优化计划

### 短期优化（1-2周）
- [ ] 优化日志系统，减少频繁的UI更新
- [ ] 实现更精细的缓存策略
- [ ] 添加网络请求防抖机制

### 中期优化（1个月）
- [ ] 实现虚拟滚动，优化大列表性能
- [ ] 添加Web Worker支持，处理CPU密集型任务
- [ ] 优化图片和资源加载

### 长期优化（3个月）
- [ ] 实现增量更新机制
- [ ] 添加离线缓存支持
- [ ] 优化启动时间和内存占用

---

**注意：** 本文档会随着性能优化的进展持续更新。如有性能问题，请参考本指南进行排查和优化。
