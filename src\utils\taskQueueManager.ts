/**
 * 任务队列管理器
 * 用于管理和控制异步任务的执行，防止消息队列溢出
 */

export interface Task {
  id: string;
  priority: number; // 优先级，数字越小优先级越高
  execute: () => Promise<any>;
  timeout?: number; // 超时时间（毫秒）
  retries?: number; // 重试次数
}

export interface TaskResult {
  id: string;
  success: boolean;
  result?: any;
  error?: Error;
  executionTime: number;
}

export class TaskQueueManager {
  private static instance: TaskQueueManager;
  private queue: Task[] = [];
  private running: Map<string, Promise<TaskResult>> = new Map();
  private maxConcurrent: number = 3; // 最大并发任务数
  private isProcessing = false;
  private stats = {
    completed: 0,
    failed: 0,
    totalExecutionTime: 0,
  };

  private constructor() {}

  static getInstance(): TaskQueueManager {
    if (!TaskQueueManager.instance) {
      TaskQueueManager.instance = new TaskQueueManager();
    }
    return TaskQueueManager.instance;
  }

  /**
   * 添加任务到队列
   */
  addTask(task: Task): void {
    // 检查是否已存在相同ID的任务
    if (this.queue.find(t => t.id === task.id) || this.running.has(task.id)) {
      console.warn(`Task with ID ${task.id} already exists, skipping`);
      return;
    }

    this.queue.push(task);
    // 按优先级排序
    this.queue.sort((a, b) => a.priority - b.priority);
    
    console.log(`Task ${task.id} added to queue (priority: ${task.priority})`);
    this.processQueue();
  }

  /**
   * 添加设备扫描任务（防抖）
   */
  addDeviceScanTask(force = false): void {
    const taskId = 'device-scan';
    
    // 如果不是强制扫描且已有扫描任务在队列中，则跳过
    if (!force && (this.queue.find(t => t.id === taskId) || this.running.has(taskId))) {
      console.log('Device scan task already queued or running, skipping');
      return;
    }

    this.addTask({
      id: taskId,
      priority: 2, // 中等优先级
      execute: async () => {
        const { invoke } = await import('@tauri-apps/api/core');
        return invoke('scan_devices');
      },
      timeout: 10000, // 10秒超时
      retries: 2,
    });
  }

  /**
   * 添加窗口状态检查任务（防抖）
   */
  addWindowStateTask(): void {
    const taskId = 'window-state-check';
    
    // 移除已存在的窗口状态检查任务
    this.queue = this.queue.filter(t => t.id !== taskId);
    
    this.addTask({
      id: taskId,
      priority: 3, // 低优先级
      execute: async () => {
        const { getCurrentWindow } = await import('@tauri-apps/api/window');
        const { invoke } = await import('@tauri-apps/api/core');
        
        const window = getCurrentWindow();
        const maximized = await window.isMaximized();
        const alwaysOnTop = await invoke('get_window_always_on_top');
        
        return { maximized, alwaysOnTop };
      },
      timeout: 5000, // 5秒超时
      retries: 1,
    });
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.running.size >= this.maxConcurrent) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0 && this.running.size < this.maxConcurrent) {
      const task = this.queue.shift()!;
      const promise = this.executeTask(task);
      this.running.set(task.id, promise);

      // 任务完成后从运行列表中移除
      promise.finally(() => {
        this.running.delete(task.id);
        // 继续处理队列
        if (this.queue.length > 0) {
          setTimeout(() => this.processQueue(), 0);
        }
      });
    }

    this.isProcessing = false;
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: Task): Promise<TaskResult> {
    const startTime = Date.now();
    let retries = task.retries || 0;

    while (retries >= 0) {
      try {
        console.log(`Executing task ${task.id} (retries left: ${retries})`);
        
        const result = await this.executeWithTimeout(task.execute(), task.timeout || 30000);
        const executionTime = Date.now() - startTime;
        
        this.stats.completed++;
        this.stats.totalExecutionTime += executionTime;
        
        console.log(`Task ${task.id} completed in ${executionTime}ms`);
        
        return {
          id: task.id,
          success: true,
          result,
          executionTime,
        };
      } catch (error) {
        retries--;
        console.warn(`Task ${task.id} failed, retries left: ${retries}`, error);
        
        if (retries < 0) {
          const executionTime = Date.now() - startTime;
          this.stats.failed++;
          
          return {
            id: task.id,
            success: false,
            error: error as Error,
            executionTime,
          };
        }
        
        // 重试前等待一段时间
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 这里不应该到达，但为了类型安全
    throw new Error(`Task ${task.id} failed after all retries`);
  }

  /**
   * 带超时的任务执行
   */
  private executeWithTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Task timeout')), timeout);
      }),
    ]);
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): boolean {
    const index = this.queue.findIndex(t => t.id === taskId);
    if (index !== -1) {
      this.queue.splice(index, 1);
      console.log(`Task ${taskId} cancelled`);
      return true;
    }
    return false;
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.queue = [];
    console.log('Task queue cleared');
  }

  /**
   * 获取队列状态
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      runningTasks: this.running.size,
      stats: { ...this.stats },
      averageExecutionTime: this.stats.completed > 0 
        ? this.stats.totalExecutionTime / this.stats.completed 
        : 0,
    };
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrent(max: number): void {
    this.maxConcurrent = Math.max(1, max);
    console.log(`Max concurrent tasks set to ${this.maxConcurrent}`);
  }
}

// 导出单例实例
export const taskQueue = TaskQueueManager.getInstance();
