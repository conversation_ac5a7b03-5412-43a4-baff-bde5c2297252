{"name": "wanjiguanjia-tauri", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --max-warnings 0", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:build:debug": "tauri build --debug", "build:all": "npm run build && npm run tauri:build", "clean": "rimraf dist src-tauri/target", "prebuild": "npm run clean", "download:drivers": "node scripts/download-drivers.js", "setup:drivers": "npm run download:drivers", "prebuild:all": "npm run setup:drivers && npm run clean"}, "dependencies": {"@fluentui/react-components": "^9.54.19", "@fluentui/react-icons": "^2.0.258", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-clipboard-manager": "^2.3.0", "@tauri-apps/plugin-dialog": "^2.3.1", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-global-shortcut": "^2.3.0", "@tauri-apps/plugin-shell": "^2.0.0", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tauri-apps/cli": "^2.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^16.3.0", "rimraf": "^6.0.1", "typescript": "~5.6.2", "typescript-eslint": "^8.38.0", "vite": "^6.0.1"}}