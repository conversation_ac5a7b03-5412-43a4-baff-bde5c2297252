/**
 * 性能监控工具
 * 用于监控应用性能，检测潜在的性能问题
 */

export interface PerformanceMetrics {
  timestamp: number;
  memoryUsage: number; // MB
  cpuUsage?: number; // 百分比
  taskQueueLength: number;
  activeTimers: number;
  activeIntervals: number;
  eventListeners: number;
}

export interface PerformanceAlert {
  type: 'memory' | 'task_queue' | 'timers' | 'intervals';
  severity: 'warning' | 'critical';
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private monitorInterval: number | null = null;
  private isMonitoring = false;
  
  // 性能阈值
  private thresholds = {
    memoryWarning: 100, // MB
    memoryCritical: 200, // MB
    taskQueueWarning: 10,
    taskQueueCritical: 20,
    timersWarning: 50,
    timersCritical: 100,
  };

  // 监控原生定时器
  private originalSetInterval = window.setInterval;
  private originalSetTimeout = window.setTimeout;
  private originalClearInterval = window.clearInterval;
  private originalClearTimeout = window.clearTimeout;
  
  private activeTimers = new Set<number>();
  private activeIntervals = new Set<number>();

  private constructor() {
    this.setupTimerMonitoring();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 开始性能监控
   */
  startMonitoring(interval = 5000): void {
    if (this.isMonitoring) {
      console.warn('Performance monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    console.log('Starting performance monitoring...');

    this.monitorInterval = this.originalSetInterval(() => {
      this.collectMetrics();
    }, interval);

    // 立即收集一次指标
    this.collectMetrics();
  }

  /**
   * 停止性能监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitorInterval) {
      this.originalClearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    console.log('Performance monitoring stopped');
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    const metrics: PerformanceMetrics = {
      timestamp: Date.now(),
      memoryUsage: this.getMemoryUsage(),
      taskQueueLength: this.getTaskQueueLength(),
      activeTimers: this.activeTimers.size,
      activeIntervals: this.activeIntervals.size,
      eventListeners: this.getEventListenerCount(),
    };

    this.metrics.push(metrics);

    // 限制指标历史记录数量
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // 检查性能警告
    this.checkPerformanceAlerts(metrics);

    console.log('Performance metrics:', metrics);
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return Math.round(memory.usedJSHeapSize / 1024 / 1024); // 转换为MB
    }
    return 0;
  }

  /**
   * 获取任务队列长度
   */
  private getTaskQueueLength(): number {
    try {
      const { taskQueue } = require('./taskQueueManager');
      return taskQueue.getStatus().queueLength;
    } catch {
      return 0;
    }
  }

  /**
   * 获取事件监听器数量（估算）
   */
  private getEventListenerCount(): number {
    // 这是一个简化的估算，实际实现可能需要更复杂的逻辑
    const elements = document.querySelectorAll('*');
    let count = 0;
    
    elements.forEach(element => {
      // 检查常见的事件类型
      const events = ['click', 'mousedown', 'mouseup', 'keydown', 'keyup', 'resize', 'scroll'];
      events.forEach(eventType => {
        if ((element as any)[`on${eventType}`]) {
          count++;
        }
      });
    });
    
    return count;
  }

  /**
   * 检查性能警告
   */
  private checkPerformanceAlerts(metrics: PerformanceMetrics): void {
    const alerts: PerformanceAlert[] = [];

    // 内存使用警告
    if (metrics.memoryUsage > this.thresholds.memoryCritical) {
      alerts.push({
        type: 'memory',
        severity: 'critical',
        message: `内存使用过高: ${metrics.memoryUsage}MB`,
        value: metrics.memoryUsage,
        threshold: this.thresholds.memoryCritical,
        timestamp: metrics.timestamp,
      });
    } else if (metrics.memoryUsage > this.thresholds.memoryWarning) {
      alerts.push({
        type: 'memory',
        severity: 'warning',
        message: `内存使用较高: ${metrics.memoryUsage}MB`,
        value: metrics.memoryUsage,
        threshold: this.thresholds.memoryWarning,
        timestamp: metrics.timestamp,
      });
    }

    // 任务队列警告
    if (metrics.taskQueueLength > this.thresholds.taskQueueCritical) {
      alerts.push({
        type: 'task_queue',
        severity: 'critical',
        message: `任务队列过长: ${metrics.taskQueueLength}`,
        value: metrics.taskQueueLength,
        threshold: this.thresholds.taskQueueCritical,
        timestamp: metrics.timestamp,
      });
    } else if (metrics.taskQueueLength > this.thresholds.taskQueueWarning) {
      alerts.push({
        type: 'task_queue',
        severity: 'warning',
        message: `任务队列较长: ${metrics.taskQueueLength}`,
        value: metrics.taskQueueLength,
        threshold: this.thresholds.taskQueueWarning,
        timestamp: metrics.timestamp,
      });
    }

    // 定时器警告
    const totalTimers = metrics.activeTimers + metrics.activeIntervals;
    if (totalTimers > this.thresholds.timersCritical) {
      alerts.push({
        type: 'timers',
        severity: 'critical',
        message: `活跃定时器过多: ${totalTimers}`,
        value: totalTimers,
        threshold: this.thresholds.timersCritical,
        timestamp: metrics.timestamp,
      });
    } else if (totalTimers > this.thresholds.timersWarning) {
      alerts.push({
        type: 'timers',
        severity: 'warning',
        message: `活跃定时器较多: ${totalTimers}`,
        value: totalTimers,
        threshold: this.thresholds.timersWarning,
        timestamp: metrics.timestamp,
      });
    }

    // 添加新警告
    alerts.forEach(alert => {
      this.alerts.push(alert);
      console.warn(`Performance Alert [${alert.severity.toUpperCase()}]:`, alert.message);
    });

    // 限制警告历史记录数量
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(-50);
    }
  }

  /**
   * 设置定时器监控
   */
  private setupTimerMonitoring(): void {
    // 监控 setInterval
    window.setInterval = (callback: Function, delay?: number, ...args: any[]) => {
      const id = this.originalSetInterval(callback, delay, ...args);
      this.activeIntervals.add(id);
      return id;
    };

    // 监控 setTimeout
    window.setTimeout = (callback: Function, delay?: number, ...args: any[]) => {
      const id = this.originalSetTimeout(() => {
        this.activeTimers.delete(id);
        callback();
      }, delay, ...args);
      this.activeTimers.add(id);
      return id;
    };

    // 监控 clearInterval
    window.clearInterval = (id: number) => {
      this.activeIntervals.delete(id);
      this.originalClearInterval(id);
    };

    // 监控 clearTimeout
    window.clearTimeout = (id: number) => {
      this.activeTimers.delete(id);
      this.originalClearTimeout(id);
    };
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const recentMetrics = this.metrics.slice(-10);
    const recentAlerts = this.alerts.slice(-10);

    return {
      currentMetrics: this.metrics[this.metrics.length - 1],
      recentMetrics,
      recentAlerts,
      summary: {
        averageMemoryUsage: recentMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) / recentMetrics.length,
        averageTaskQueueLength: recentMetrics.reduce((sum, m) => sum + m.taskQueueLength, 0) / recentMetrics.length,
        totalActiveTimers: this.activeTimers.size + this.activeIntervals.size,
        alertCount: this.alerts.length,
      },
    };
  }

  /**
   * 清除历史数据
   */
  clearHistory(): void {
    this.metrics = [];
    this.alerts = [];
    console.log('Performance history cleared');
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();
